"""
Progressive loading system for Matrix-F to improve UI responsiveness.
Provides chunked processing with progress feedback and yield points.
"""

import time
import threading
import queue
from typing import Any, Callable, List, Dict, Optional, Generator, Tuple
from dataclasses import dataclass
from datetime import datetime
import psutil


@dataclass
class ProgressUpdate:
    """Progress update information for UI feedback."""
    current_step: int
    total_steps: int
    current_item: int
    total_items: int
    stage_name: str
    estimated_remaining_seconds: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None


class ProgressiveLoader:
    """
    Progressive loader that processes data in chunks with UI responsiveness.
    """
    
    def __init__(self, 
                 max_chunk_time_seconds: float = 0.5,
                 min_chunk_size: int = 1,
                 max_chunk_size: int = 100,
                 progress_callback: Optional[Callable[[ProgressUpdate], None]] = None):
        """
        Initialize progressive loader.
        
        Args:
            max_chunk_time_seconds: Maximum time to spend processing before yielding
            min_chunk_size: Minimum items to process in each chunk
            max_chunk_size: Maximum items to process in each chunk
            progress_callback: Optional callback for progress updates
        """
        self.max_chunk_time = max_chunk_time_seconds
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        self.progress_callback = progress_callback
        
        # Progress tracking
        self.start_time = None
        self.items_processed = 0
        self.total_items = 0
        self.current_stage = ""
        
        # Performance monitoring
        self.chunk_times = []
        self.last_yield_time = None
    
    def process_with_progress(self, 
                            items: List[Any], 
                            processor_func: Callable[[Any], Any],
                            stage_name: str = "Processing") -> List[Any]:
        """
        Process items with progressive loading and progress feedback.
        
        Args:
            items: List of items to process
            processor_func: Function to process each item
            stage_name: Name of the current processing stage
            
        Returns:
            List of processed results
        """
        self.start_time = time.time()
        self.items_processed = 0
        self.total_items = len(items)
        self.current_stage = stage_name
        self.last_yield_time = self.start_time
        
        results = []
        chunk_start_idx = 0
        
        print(f"Starting progressive processing: {stage_name} ({self.total_items} items)")
        
        while chunk_start_idx < len(items):
            # Determine chunk size based on recent performance
            chunk_size = self._calculate_optimal_chunk_size()
            chunk_end_idx = min(chunk_start_idx + chunk_size, len(items))
            
            # Process chunk
            chunk_start_time = time.time()
            chunk_results = []
            
            for i in range(chunk_start_idx, chunk_end_idx):
                try:
                    result = processor_func(items[i])
                    chunk_results.append(result)
                    self.items_processed += 1
                except Exception as e:
                    print(f"Error processing item {i}: {e}")
                    continue
                
                # Check if we should yield control
                current_time = time.time()
                if (current_time - chunk_start_time) > self.max_chunk_time:
                    break
            
            results.extend(chunk_results)
            
            # Record chunk performance
            chunk_time = time.time() - chunk_start_time
            self.chunk_times.append(chunk_time)
            
            # Send progress update
            self._send_progress_update()
            
            # Yield control to allow UI updates
            self._yield_control()
            
            chunk_start_idx = chunk_end_idx
        
        total_time = time.time() - self.start_time
        print(f"Progressive processing completed: {stage_name} in {total_time:.2f}s")
        
        return results
    
    def process_batches_with_progress(self,
                                    batch_items: List[List[Any]],
                                    batch_processor_func: Callable[[List[Any]], List[Any]],
                                    stage_name: str = "Batch Processing") -> List[Any]:
        """
        Process batches with progressive loading.
        
        Args:
            batch_items: List of batches to process
            batch_processor_func: Function to process each batch
            stage_name: Name of the current processing stage
            
        Returns:
            Flattened list of all results
        """
        self.start_time = time.time()
        self.items_processed = 0
        self.total_items = len(batch_items)
        self.current_stage = stage_name
        self.last_yield_time = self.start_time
        
        all_results = []
        
        print(f"Starting progressive batch processing: {stage_name} ({self.total_items} batches)")
        
        for batch_idx, batch in enumerate(batch_items):
            batch_start_time = time.time()
            
            try:
                batch_results = batch_processor_func(batch)
                all_results.extend(batch_results)
                self.items_processed += 1
            except Exception as e:
                print(f"Error processing batch {batch_idx}: {e}")
                continue
            
            # Record batch performance
            batch_time = time.time() - batch_start_time
            self.chunk_times.append(batch_time)
            
            # Send progress update
            self._send_progress_update()
            
            # Yield control after each batch
            self._yield_control()
        
        total_time = time.time() - self.start_time
        print(f"Progressive batch processing completed: {stage_name} in {total_time:.2f}s")
        
        return all_results
    
    def _calculate_optimal_chunk_size(self) -> int:
        """Calculate optimal chunk size based on recent performance."""
        if not self.chunk_times:
            return self.min_chunk_size
        
        # Use average of recent chunk times to estimate optimal size
        recent_times = self.chunk_times[-5:]  # Last 5 chunks
        avg_time = sum(recent_times) / len(recent_times)
        
        if avg_time < self.max_chunk_time * 0.5:
            # Chunks are processing quickly, increase size
            return min(self.max_chunk_size, self.min_chunk_size * 2)
        elif avg_time > self.max_chunk_time * 0.8:
            # Chunks are taking too long, decrease size
            return max(self.min_chunk_size, self.min_chunk_size // 2)
        else:
            # Current size is good
            return self.min_chunk_size
    
    def _send_progress_update(self):
        """Send progress update to callback if available."""
        if not self.progress_callback:
            return
        
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # Estimate remaining time
        if self.items_processed > 0:
            time_per_item = elapsed_time / self.items_processed
            remaining_items = self.total_items - self.items_processed
            estimated_remaining = time_per_item * remaining_items
        else:
            estimated_remaining = None
        
        # Get system resource usage
        memory = psutil.virtual_memory()
        cpu_usage = psutil.cpu_percent(interval=0)
        
        progress = ProgressUpdate(
            current_step=self.items_processed,
            total_steps=self.total_items,
            current_item=self.items_processed,
            total_items=self.total_items,
            stage_name=self.current_stage,
            estimated_remaining_seconds=estimated_remaining,
            memory_usage_mb=memory.used / (1024**2),
            cpu_usage_percent=cpu_usage
        )
        
        try:
            self.progress_callback(progress)
        except Exception as e:
            print(f"Error in progress callback: {e}")
    
    def _yield_control(self):
        """Yield control to allow UI updates and other processes."""
        current_time = time.time()
        
        # Always yield after processing
        time.sleep(0.001)  # Small sleep to allow UI updates
        
        # Additional yield if it's been a while
        if current_time - self.last_yield_time > 1.0:
            time.sleep(0.01)  # Longer sleep for UI responsiveness
            self.last_yield_time = current_time


class ProgressivePortfolioProcessor:
    """
    Specialized progressive processor for portfolio optimization tasks.
    """
    
    def __init__(self, progress_callback: Optional[Callable[[ProgressUpdate], None]] = None):
        self.progress_callback = progress_callback
        self.loader = ProgressiveLoader(
            max_chunk_time_seconds=0.3,  # Shorter chunks for portfolio processing
            min_chunk_size=5,
            max_chunk_size=50,
            progress_callback=progress_callback
        )
    
    def process_portfolio_combinations(self, 
                                     valid_combos: List[Any],
                                     processor_func: Callable[[Any], Any]) -> List[Any]:
        """
        Process portfolio combinations with progressive loading.
        
        Args:
            valid_combos: List of portfolio combinations to process
            processor_func: Function to process each combination
            
        Returns:
            List of processed portfolio results
        """
        return self.loader.process_with_progress(
            valid_combos, 
            processor_func, 
            "Portfolio Optimization"
        )
    
    def process_frontier_calculations(self,
                                    frontier_groups: List[List[Any]],
                                    frontier_func: Callable[[List[Any]], List[Any]]) -> List[Any]:
        """
        Process efficient frontier calculations with progressive loading.
        
        Args:
            frontier_groups: List of portfolio groups for frontier calculation
            frontier_func: Function to calculate frontier for each group
            
        Returns:
            List of frontier results
        """
        return self.loader.process_batches_with_progress(
            frontier_groups,
            frontier_func,
            "Efficient Frontier Calculation"
        )


# Global progress tracking
_current_progress: Optional[ProgressUpdate] = None
_progress_lock = threading.Lock()


def get_current_progress() -> Optional[ProgressUpdate]:
    """Get the current progress information."""
    with _progress_lock:
        return _current_progress


def _update_global_progress(progress: ProgressUpdate):
    """Update global progress information."""
    global _current_progress
    with _progress_lock:
        _current_progress = progress
        # Print progress for debugging
        percent = (progress.current_step / progress.total_steps) * 100 if progress.total_steps > 0 else 0
        print(f"Progress: {progress.stage_name} - {percent:.1f}% ({progress.current_step}/{progress.total_steps})")


# Global progressive processor instance
_global_processor: Optional[ProgressivePortfolioProcessor] = None


def get_progressive_processor() -> ProgressivePortfolioProcessor:
    """Get the global progressive processor instance."""
    global _global_processor
    if _global_processor is None:
        _global_processor = ProgressivePortfolioProcessor(progress_callback=_update_global_progress)
    return _global_processor
