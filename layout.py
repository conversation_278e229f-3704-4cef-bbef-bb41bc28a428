from dash import html, dcc
from app import app
import MetaTrader5 as mt5
import plotly.graph_objects as go # Import plotly
layout = html.Div([
    html.H2("MPT Tracker", style={'color': 'white', 'marginTop': '20px'}),
    html.Div(id='mpt-weights-table', style={'marginBottom': '15px'}),  # New div for the weights table
    html.Div([
        dcc.Input(id='portfolio-allocation', type='text', 
                placeholder="Enter symbol:weight pairs e.g., EURUSD:0.4, GBPUSD:0.3", 
                style={'width': '60%'}),
        html.Button("Update MPT", id="update-mpt", n_clicks=0)
    ], style={'padding': '10px'}),
    html.Div([
        html.Label("Size:", style={'color': 'white', 'marginRight': '10px'}),
        dcc.Input(id='mpt-size', type='number', value=0.80, step=0.05, 
                 style={'width': '80px', 'marginRight': '15px'}),
        html.But<PERSON>("Use Min Variance", id="use-minvar", n_clicks=0),
        html.<PERSON><PERSON>("Use Max Sharpe", id="use-maxsharpe", n_clicks=0),
        html.Button("Use Max Sortino", id="use-maxsortino", n_clicks=0),
        html.Button("Use Max Omega", id="use-maxomega", n_clicks=0),
        html.Button("Use Max Calmar", id="use-maxcalmar", n_clicks=0),
        html.Button("Use Max CF Sharpe", id="use-maxcf", n_clicks=0),
        html.Button("Use Max Return", id="use-maxreturn", n_clicks=0),
        html.Button("Invert", id="use-invert", n_clicks=0),
        html.Span(" ", style={'margin': '0 15px'}),  # Small space
        html.Button("USD", id="use-usd", n_clicks=0),
        html.Button("EUR", id="use-eur", n_clicks=0),
        html.Button("GBP", id="use-gbp", n_clicks=0),
        html.Button("AUD", id="use-aud", n_clicks=0),
        html.Button("NZD", id="use-nzd", n_clicks=0),
        html.Button("CAD", id="use-cad", n_clicks=0),
        html.Button("CHF", id="use-chf", n_clicks=0),
        html.Button("JPY", id="use-jpy", n_clicks=0)
    ], style={'padding': '10px'}),
    dcc.Store(id='mpt-weights-store'),
    dcc.Store(id='optimization-running-store', data=False),
    dcc.Store(id='recommended-portfolios-store'),
    dcc.Store(id='recommended-cvar-portfolios-store'),
    dcc.Store(id='clicked-portfolio-store'), # Store for last clicked portfolio data
    dcc.Graph(id='mpt-tracker',
            figure=go.Figure(layout=go.Layout( # Add default figure with black background
                title="Modern Portfolio Theory Tracker",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))
        ),
    dcc.Interval(id='interval-component1', interval=15000, n_intervals=0),
    # New textarea for MPT weights in the MPT Tracker section
    html.Div([
        html.H4("MPT Tracker Weights", style={'marginBottom': '5px', 'color': 'white'}),
        dcc.Textarea(
            id='mpt-tracker-weights',
            readOnly=True,
            style={
                'width': '90%',
                'height': '110px',
                'backgroundColor': '#333',
                'color': 'white',
                'fontFamily': 'monospace',
                'fontSize': '18px',
                'border': '1px solid white',
                'padding': '10px',
                'lineHeight': '28px'
            }
        )
    ], style={'marginTop': '15px', 'marginBottom': '15px', 'padding': '10px', 'backgroundColor': 'black'}),
    html.H3("Modern Portfolio Theory Optimization", style={'color': 'white', 'marginTop': '20px'}),
    html.Div([
        dcc.Interval(id='interval-optim', interval=300000, n_intervals=0, disabled=False),
        dcc.RadioItems(
            id='frontier-option',
            options=[
                {'label': '24 Rolling Hours', 'value': '24'},
                {'label': "Today's M15", 'value': 'today'},
                {'label': '240 (216+today)', 'value': '240'},
                {'label': '120 (96+today)', 'value': '120'},
                {'label': '72 (48+today)', 'value': '72'}
            ],
            value='today',  # Changed from '24' to 'today' to make it the default
            labelStyle={'display': 'inline-block', 'margin-right': '10px'},
            style={'padding': '10px', 'backgroundColor': 'black', 'color': 'white'}
        ),
        html.Button("Show Optimization Table", id="show-portfolio-table", n_clicks=0, 
                style={'backgroundColor': 'darkblue', 'color': 'white', 'marginTop': '15px', 'padding': '10px'}),
        
        html.Div(id='portfolio-table-container', style={'display': 'none'}, children=[
            html.Table(id='portfolio-table', style={'width': '100%', 'color': 'white', 'border': '1px solid white'})
        ]),
        #html.Table(id='portfolio-table', style={'width': '100%', 'color': 'white', 'border': '1px solid white'}),
        dcc.Graph(id='efficient-frontier',
            figure=go.Figure(layout=go.Layout( # Add default figure with black background
                title="Efficient Frontier",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))
        ),
        html.Div([
            html.Label("Size:"),
            dcc.Input(id='annotation-total-weight', type='number', value=0.20, step=0.01, debounce=True, style={'width': '100px', 'marginLeft': '10px'})
        ], style={'padding': '10px', 'display': 'flex', 'alignItems': 'center', 'color': 'white', 'backgroundColor': 'black'}),
        dcc.Graph(
            id='portfolio-returns',
            figure=go.Figure(layout=go.Layout( # Add default figure with black background
                title="Portfolio Cumulative Returns (Click a dot in the Efficient Frontier)",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))
        ),
        html.Div([
        html.Button("Load Risk Recommendations", id="use-risk-recommendations", n_clicks=0, style={'backgroundColor': 'darkblue', 'color': 'white', 'marginRight': '10px'}),
        html.Button("Load CVaR Recommendations", id="use-cvar-recommendations", n_clicks=0, style={'backgroundColor': 'darkred', 'color': 'white'})
        ], style={'padding': '10px', 'marginTop': '10px'}),
        # New div for combined weights that can be copied
        html.Div([
            html.H4("Combined Portfolio MPT Weights", style={'marginBottom': '5px', 'color': 'white'}),
            dcc.Textarea(
                id='combined-mpt-weights',
                readOnly=True,
                style={
                    'width': '90%',
                    'height': '90px',
                    'backgroundColor': '#333',
                    'color': 'white',
                    'fontFamily': 'monospace',
                    'fontSize': '18px',  # Added font size
                    'border': '1px solid white',
                    'padding': '10px',
                    'lineHeight': '28px'  # Added line height to ensure text is vertically centered
                }
            )
        ], style={'marginTop': '15px', 'marginBottom': '15px', 'padding': '10px', 'backgroundColor': 'black'})
    ], style={'backgroundColor': 'black', 'padding': '20px'}),
    html.H4("CVaR rotation", style={'color': 'white', 'marginTop': '20px'}),
    html.Div([
        dcc.Graph(id='cvar-frontier',
            figure=go.Figure(layout=go.Layout( # Add default figure with black background
                title="CVaR Frontier",
                paper_bgcolor='black',
                plot_bgcolor='black',
                font=dict(color='white')
            ))
        )
    ], style={'backgroundColor': 'black', 'padding': '20px'}),
    # '''
    # # --- Market Phase Plots ---
    # html.H3("Market Phase Analysis", style={'color': 'white', 'marginTop': '20px'}),
    # html.Div([
    #     dcc.Graph(id='market-phase-dispersion',
    #         figure=go.Figure(layout=go.Layout(title="Dispersion / Retracement / HMM / BCPD", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-cusum',
    #         figure=go.Figure(layout=go.Layout(title="CUSUM on Dispersion", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-spectral',
    #         figure=go.Figure(layout=go.Layout(title="Dominant Period (Spectral Analysis)", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-sentiment',
    #         figure=go.Figure(layout=go.Layout(title="Sentiment Metrics", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-normalized-returns',
    #         figure=go.Figure(layout=go.Layout(title="Normalized Basket Returns (Z-scores)", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-area-dispersion',
    #         figure=go.Figure(layout=go.Layout(title="Area Dispersion", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    #     dcc.Graph(id='market-phase-cumulative-usd',
    #         figure=go.Figure(layout=go.Layout(title="Cumulative USD Basket Returns", paper_bgcolor='black', plot_bgcolor='black', font=dict(color='white')))
    #     ),
    # ], style={'backgroundColor': 'black', 'padding': '20px'}),
    # '''
], style={'backgroundColor': 'black', 'color': 'white', 'padding': '20px'})
